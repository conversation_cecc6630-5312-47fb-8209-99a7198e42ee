import json
import pandas as pd
from openpyxl.styles import Font, PatternFill, Alignment

def export_to_excel(datasets):
    """
    将数据集信息导出到Excel文件，包含格式化
    """
    try:
        print("\n正在导出到Excel文件...")
        
        # 准备数据用于DataFrame
        excel_data = []
        for dataset in datasets:
            # 处理all_links字段，将列表转换为字符串
            all_links_str = "; ".join(dataset.get('all_links', [])) if dataset.get('all_links') else ""
            
            excel_data.append({
                '数据集名称': dataset.get('dataset_name', ''),
                '数据集类别': dataset.get('dataset_category', ''),
                '主要链接': dataset.get('link', ''),
                '所有链接': all_links_str,
                '描述': dataset.get('description', '')
            })
        
        # 创建DataFrame
        df = pd.DataFrame(excel_data)
        
        # 导出到Excel文件并格式化
        excel_filename = "safetyprompts_datasets.xlsx"
        
        # 使用ExcelWriter进行更好的格式化
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='数据集列表', index=False)
            
            # 获取工作表对象
            worksheet = writer.sheets['数据集列表']
            
            # 设置列宽
            column_widths = {
                'A': 25,  # 数据集名称
                'B': 20,  # 数据集类别
                'C': 50,  # 主要链接
                'D': 80,  # 所有链接
                'E': 100  # 描述
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
            
            # 设置标题行格式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")
            
            # 设置文本换行
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical="top")
        
        print(f"✅ 数据已成功导出到Excel文件: {excel_filename}")
        print(f"   总共导出了 {len(datasets)} 个数据集")
        
        # 显示一些统计信息
        category_counts = df['数据集类别'].value_counts()
        print(f"\n📊 数据集类别统计:")
        for category, count in category_counts.items():
            print(f"   {category}: {count} 个")
        
        # 创建分类汇总表
        create_category_summary(df, excel_filename)
            
    except Exception as e:
        print(f"❌ 导出Excel时发生错误: {e}")


def create_category_summary(df, excel_filename):
    """
    创建数据集类别汇总表
    """
    try:
        # 创建类别汇总
        category_summary = df.groupby('数据集类别').agg({
            '数据集名称': 'count',
            '主要链接': lambda x: x.notna().sum()
        }).rename(columns={
            '数据集名称': '数据集数量',
            '主要链接': '有链接数量'
        })
        
        category_summary['有链接比例'] = (category_summary['有链接数量'] / category_summary['数据集数量'] * 100).round(1)
        category_summary = category_summary.reset_index()
        
        # 添加到Excel文件的新工作表
        with pd.ExcelWriter(excel_filename, engine='openpyxl', mode='a') as writer:
            category_summary.to_excel(writer, sheet_name='类别统计', index=False)
            
            # 格式化统计表
            worksheet = writer.sheets['类别统计']
            
            # 设置列宽
            for col in ['A', 'B', 'C', 'D']:
                worksheet.column_dimensions[col].width = 25
            
            # 设置标题行格式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")
        
        print(f"✅ 类别统计表已添加到Excel文件")
        
    except Exception as e:
        print(f"⚠️ 创建类别汇总时发生错误: {e}")


if __name__ == "__main__":
    # 从JSON文件读取数据
    try:
        with open('safetyprompts_datasets_manual.json', 'r', encoding='utf-8') as f:
            datasets = json.load(f)
        
        print(f"从JSON文件读取了 {len(datasets)} 个数据集")
        export_to_excel(datasets)
        
    except FileNotFoundError:
        print("❌ 未找到 safetyprompts_datasets_manual.json 文件")
        print("请先运行 crawl_datasets.py 来抓取数据")
    except Exception as e:
        print(f"❌ 读取JSON文件时发生错误: {e}")
