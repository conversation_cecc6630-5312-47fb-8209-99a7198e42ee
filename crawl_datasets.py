import asyncio
import json
import os
import re  # 导入正则表达式模块
# 根据用户提供的最新用法，我们导入 AsyncWebCrawler
from crawl4ai import AsyncWebCrawler

# 检查 PyTorch 和 transformers 是否已安装
try:
    import torch
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
except ImportError:
    print("错误：请先安装 PyTorch 和 Transformers。")
    print("运行: pip install torch transformers accelerate")
    exit()

# --- 步骤 1: 创建一个与 Crawl4AI 兼容的本地 LLM 封装类 ---
# (这部分保持不变)
class HuggingFaceLocalLLM:
    """
    一个封装了 Hugging Face transformers pipeline 的类，
    使其能够像一个异步 LLM 一样被 Crawl4AI 调用。
    """
    def __init__(self, model_name="bigscience/bloomz-560m"):
        """
        初始化时加载模型和分词器。
        
        Args:
            model_name (str): 要从 Hugging Face Hub 加载的模型名称。
        """
        print(f"正在加载模型 '{model_name}'... 这可能需要一些时间并且会占用较多内存。")
        
        # 指定模型下载到本地目录
        cache_path = "./models_cache"
        os.makedirs(cache_path, exist_ok=True)
        print(f"模型将下载并缓存到当前目录下的 '{cache_path}' 文件夹中。")

        # 检查是否有可用的 GPU
        device = 0 if torch.cuda.is_available() else -1
        if device == 0:
            print("检测到 GPU，将在 GPU 上运行模型。")
        else:
            print("未检测到 GPU，将在 CPU 上运行模型（速度可能较慢）。")
            
        # 使用 pipeline 加载一个文本生成模型
        self.pipeline = pipeline(
            "text-generation",
            model=model_name,
            model_kwargs={'cache_dir': cache_path},
            device=device,
            torch_dtype=torch.bfloat16 if device == 0 else torch.float32
        )
        print("模型加载完成。")

    async def acreate(self, **kwargs):
        """
        一个异步方法，用于生成文本。Crawl4AI 会调用这个方法。
        """
        prompt = kwargs.get("prompt", "")
        if not prompt:
            return ""

        loop = asyncio.get_running_loop()
        
        try:
            result = await loop.run_in_executor(
                None, self.pipeline, prompt, max_new_tokens=2048, num_return_sequences=1 # 增加生成长度以容纳更多数据集
            )
            generated_text = result[0]['generated_text']
            return generated_text[len(prompt):]
        except Exception as e:
            print(f"模型生成时出错: {e}")
            return "模型处理时发生错误。"


async def main():
    """
    主函数，执行抓取和分析任务。
    """
    print("--- 任务开始 ---")
    
    # --- 步骤 2: 实例化本地 LLM ---
    try:
        local_llm = HuggingFaceLocalLLM()
    except Exception as e:
        print(f"初始化本地 LLM 失败: {e}")
        return

    # --- 步骤 3: 定义基于网页结构分析的全新提示 ---
    prompt_template = """
你是一个专业的网页结构分析和信息提取AI。请仔细分析以下从 SafetyPrompts.com 网站抓取的HTML内容。

你的任务是识别出所有的安全测试数据集，并为每一个数据集提取详细信息。这些数据集通常在一个类别标题（如 "Broad Safety Datasets"）之下。

对于每一个独立的数据集条目，请提取以下四个字段：
1. "dataset_name": 数据集的具体名称 (例如 "HarmEval")。
2. "dataset_category": 该数据集所属的大类别 (例如 "Broad Safety Datasets")。
3. "link": 指向该数据集的外部链接地址 (例如 "https://github.com/NeuralSentinel/SafeInfer")。如果找不到链接，请使用 null。
4. "description": 关于该数据集的简介文字。

请将所有提取到的数据集信息格式化为一个JSON数组。不要在JSON数组前后添加任何其他文字或解释。

下面是一个你应当遵循的输出格式示例：
[
  {
    "dataset_name": "HarmEval",
    "dataset_category": "Broad Safety Datasets",
    "link": "https://github.com/NeuralSentinel/SafeInfer",
    "description": "A benchmark for evaluating the safety of large language models across 8 different harm types."
  }
]

现在，请根据下面的网页内容进行分析：
"""

    # --- 步骤 4: 使用新的 AsyncWebCrawler 语法初始化并运行 Crawl4AI ---
    print("\n正在启动爬虫抓取 SafetyPrompts.com，请稍候...")
    result_text = ""
    async with AsyncWebCrawler() as crawler:
        # 将所有参数传递给 arun 方法
        # 当提供了 llm 和 prompt 时，我们假设返回的是 LLM 的直接输出
        result = await crawler.arun(
            url="https://safetyprompts.com/",
            llm=local_llm,
            prompt=prompt_template,
            css_selector="div.prompts-container"
        )
        # 从 CrawlResult 对象中提取 .markdown 属性，这其中包含了LLM的输出
        result_text = result.markdown if result and hasattr(result, 'markdown') else ""

    # --- 步骤 5: 处理并打印结果 ---
    print("\n--- LLM 返回的原始文本 ---")
    print(result_text)

    print("\n--- 尝试解析 JSON 并格式化输出 ---")
    try:
        if not result_text:
            print("LLM 没有返回任何内容。")
            return
            
        # **错误修复**: 使用正则表达式从可能混乱的文本中提取 JSON 数组
        # 这会查找从第一个 '[' 到最后一个 ']' 的所有内容
        match = re.search(r'\[.*\]', result_text, re.DOTALL)
        
        if match:
            json_str = match.group(0)
            data = json.loads(json_str)
            print("解析成功！提取到的数据集信息：")
            for i, dataset in enumerate(data, 1):
                print(f"\n--- 数据集 {i} ---")
                print(f"  数据集名称: {dataset.get('dataset_name', 'N/A')}")
                print(f"  数据集类别: {dataset.get('dataset_category', 'N/A')}")
                print(f"  数据集链接地址: {dataset.get('link', 'N/A')}")
                print(f"  数据集简介: {dataset.get('description', 'N/A')}")
            
            # 将结果保存到文件
            output_filename = "safetyprompts_datasets.json"
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n✅ 结果已成功保存到文件: {output_filename}")

        else:
            print("在返回结果中未找到有效的 JSON 数组。")
    except json.JSONDecodeError as e:
        print(f"解析 JSON 失败。可能是模型没有完全按照指令返回标准格式。错误: {e}")
    except Exception as e:
        print(f"处理结果时发生未知错误: {e}")

    print("\n--- 任务结束 ---")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
