import requests
from bs4 import BeautifulSoup
import pandas as pd

index = range(0, 250, 25)

def download_htmls():
    htmls = []
    for i in index:
        url = 'https://movie.douban.com/top250?start=' + str(i) + '&filter='
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 Edg/132.0.0.0',
                   'cookie': 'bid=eokgzCeQqj4; ll="118209"; douban-fav-remind=1; ap_v=0,6.0; push_noty_num=0; push_doumail_num=0; dbcl2="272206316:QvlNPfv9KZQ"; ck=IByR'}
        html = requests.get(url,headers=headers).text
        htmls.append(html)
    return htmls
 
htmls = download_htmls()


def parse_html(html):
    soup = BeautifulSoup(html, 'html.parser')
    items = soup.find('div', class_='article').find('ol', class_='grid_view').find_all('div', class_='item')
    data = []
    for item in items:
        rank = item.find('div', class_='pic').find('em').get_text()
        title = item.find('div', class_='hd').find('span', class_='title').get_text()
        stars = item.find('div', class_='info').find('div', class_='bd').find('div', class_='star').find_all('span')
        rating_stars = stars[0]['class'][0].replace('rating', '').replace('-t', '')
        rating_num = stars[1].get_text()
        commonts_num = stars[3].get_text()
        data.append({'rank': rank, 'title': title, 'rating_stars': rating_stars, 'rating_num': rating_num, 'commonts_num': commonts_num})
    return data

all_datas = []

for html in htmls:
    all_datas.extend(parse_html(html))

df = pd.DataFrame(all_datas)
df.to_excel('douban_top250.xlsx', index=False)
