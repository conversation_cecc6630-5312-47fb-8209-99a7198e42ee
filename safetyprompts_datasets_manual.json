[{"dataset_name": "Broad Safety Datasets", "link": null, "all_links": [], "description": "55 \"Broad Safety\" datasets cover several aspects of LLM safety.", "dataset_category": "Table of Contents"}, {"dataset_name": "Narrow Safety Datasets", "link": null, "all_links": [], "description": "26 \"Narrow Safety\" datasets focus only on one specific aspect of LLM safety.", "dataset_category": "Table of Contents"}, {"dataset_name": "Value Alignment Datasets", "link": null, "all_links": [], "description": "23 \"Value Alignment\" datasets are concerned with the ethical, moral or social behaviour of LLMs.", "dataset_category": "Table of Contents"}, {"dataset_name": "Bias Datasets", "link": null, "all_links": [], "description": "35 \"Bias\" datasets evaluate sociodemographic biases in LLMs.", "dataset_category": "Table of Contents"}, {"dataset_name": "Other Datasets", "link": null, "all_links": [], "description": "10 \"Other\" datasets serve more specialised purposes.", "dataset_category": "Table of Contents"}, {"dataset_name": "HarmEval", "link": "https://github.com/NeuralSentinel/SafeInfer", "all_links": ["https://github.com/NeuralSentinel/SafeInfer", "https://ojs.aaai.org/index.php/AAAI/article/view/34927"], "description": "**550 prompts**. Each prompt is an unsafe question or instruction. HarmEval was created to **evaluate LLM misuse scenarios**. The dataset language is English. Dataset entries are **machine-written** : based on OpenAI and Meta usage policies. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "JBBBehaviours", "link": "https://huggingface.co/datasets/JailbreakBench/JBB-Behaviors", "all_links": ["https://huggingface.co/datasets/JailbreakBench/JBB-Behaviors", "https://github.com/JailbreakBench/jailbreakbench", "https://arxiv.org/abs/2404.01318"], "description": "**100 prompts**. Each prompt is an unsafe question or instruction. JBBBehaviours was created to **evaluate effectiveness of different jailbreaking methods**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AdvBench and Harmbench, plus hand-written examples. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SGBench", "link": "https://github.com/MurrayTom/SG-Bench", "all_links": ["https://github.com/MurrayTom/SG-Bench", "https://arxiv.org/abs/2410.21965"], "description": "**1,442 prompts**. Each prompt is a malicious instruction or question, which comes in multiple formats (direct query, jailbreak, multiple-choice or safety classification) SGBench was created to **evaluate the generalisation of LLM safety across various tasks and prompt types**. The dataset language is English. Dataset entries are **human-written** : sampled from AdvBench, HarmfulQA, BeaverTails and SaladBench, then expanded using templates and LLMs. The dataset license is GPL 3.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "StrongREJECT", "link": "https://github.com/alexandrasouly/strongreject/tree/main", "all_links": ["https://github.com/alexandrasouly/strongreject/tree/main", "https://arxiv.org/abs/2402.10260"], "description": "**313 prompts**. Each prompt is a 'forbidden question' in one of six categories. StrongREJECT was created to **better investigate the effectiveness of different jailbreaking techniques**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written and curated questions from existing datasets, plus LLM-generated prompts verified for quality and relevance. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://huggingface.co/datasets/allenai/wildjailbreak", "all_links": ["https://huggingface.co/datasets/allenai/wildjailbreak", "https://openreview.net/forum?id=n5R6TvBVcX&noteId=f3XHMKAJeK"], "description": "**261,534 conversations**. Each conversation is single-turn, containing a potentially unsafe prompt and a safe model response. WildJailbreak was created to **train LLMs to be safe**. The dataset language is English. Dataset entries are **machine-written** : generated by different LLMs prompted with example prompts and jailbreak techniques. The dataset license is ODC BY. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ForbiddenQuestions", "link": "https://github.com/verazuo/jailbreak_llms", "all_links": ["https://github.com/verazuo/jailbreak_llms", "https://dl.acm.org/doi/10.1145/3658644.3670388"], "description": "**390 prompts**. Each prompt is a question targetting behaviour disallowed by OpenAI. ForbiddenQuestions was created to **evaluate whether LLMs answer questions that violate OpenAI's usage policy**. The dataset language is English. Dataset entries are **machine-written** : GPT-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "CoSafe", "link": "https://github.com/ErxinYu/CoSafe-Dataset", "all_links": ["https://github.com/ErxinYu/CoSafe-Dataset", "https://aclanthology.org/2024.emnlp-main.968/"], "description": "**1,400 conversations**. Each conversation is multi-turn with the final question being unsafe. CoSafe was created to **evaluating LLM safety in dialogue coreference**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from BeaverTails, then expanded with GPT-4 into multi-turn conversations. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ArabicAdvBench", "link": "https://github.com/SecureDL/arabic_jailbreak/tree/main/data", "all_links": ["https://github.com/SecureDL/arabic_jailbreak/tree/main/data", "https://aclanthology.org/2024.emnlp-main.1034"], "description": "**520 prompts**. Each prompt is a harmful instruction with an associated jailbreak prompt. ArabicAdvBench was created to **evaluate safety risks of LLMs in (different forms of) Arabic**. The dataset language is Arabic. Dataset entries are **machine-written** : translated from AdvBench (which is machine-generated). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AyaRedTeaming", "link": "https://huggingface.co/datasets/CohereForAI/aya_redteaming", "all_links": ["https://huggingface.co/datasets/CohereForAI/aya_redteaming", "https://aclanthology.org/2024.emnlp-main.671/"], "description": "**7,419 prompts**. Each prompt is a harmful question or instruction. AyaRedTeaming was created to **provide a testbed for exploring alignment across global and local preferences**. The dataset languages are Arabic, English, Filipino, French, Hindi, Russian, Serbian and Spanish. Dataset entries are **human-written** : written by paid native-speaking annotators. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "UltraSafety", "link": "https://huggingface.co/datasets/openbmb/UltraSafety", "all_links": ["https://huggingface.co/datasets/openbmb/UltraSafety", "https://aclanthology.org/2024.emnlp-main.85/"], "description": "**3,000 prompts**. Each prompt is a harmful instruction with an associated jailbreak prompt. UltraSafety was created to **create data for safety fine-tuning of LLMs**. The dataset language is English. Dataset entries are **machine-written** : sampled from AdvBench and MaliciousInstruct, then expanded with SelfInstruct. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "PolygloToxicityPrompts", "link": "https://huggingface.co/datasets/ToxicityPrompts/PolygloToxicityPrompts", "all_links": ["https://huggingface.co/datasets/ToxicityPrompts/PolygloToxicityPrompts", "https://github.com/kpriyanshu256/polyglo-toxicity-prompts", "https://arxiv.org/abs/2405.09373"], "description": "**425,000 prompts**. Each prompt is an unfinished document. PolygloToxicityPrompts was created to **evaluate propensity of LLMs to generate toxic content across 17 languages**. The dataset languages are Arabic, Chinese, Czech, Dutch, English, French, German, Hindi, Indonesian, Italian, Japanese, Korean, Polish, Portuguese, Russian, Spanish and Swedish . Dataset entries are **human-written** : sampled from mC4 and The Pile. The dataset license is AI2 ImpACT - LR. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "CHiSafetyBench", "link": "https://github.com/UnicomAI/UnicomBenchmark/tree/main/CHiSafetyBench", "all_links": ["https://github.com/UnicomAI/UnicomBenchmark/tree/main/CHiSafetyBench", "https://arxiv.org/abs/2406.10311"], "description": "**1,861 multiple-choice questions**. CHiSafetyBench was created to **evaluate LLM capabilities to identify risky content and refuse to answer risky questions in Chinese**. The dataset language is Chinese. Dataset entries were created in a **hybrid** fashion: sampled from SafetyBench and SafetyPrompts, plus prompts generated by GPT-3.5. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SafetyBench", "link": "https://huggingface.co/datasets/thu-coai/SafetyBench", "all_links": ["https://huggingface.co/datasets/thu-coai/SafetyBench", "https://github.com/thu-coai/SafetyBench", "https://aclanthology.org/2024.acl-long.830/"], "description": "**11,435 multiple-choice questions**. SafetyBench was created to **evaluate LLM safety with multiple choice questions**. The dataset languages are English and Chinese. Dataset entries were created in a **hybrid** fashion: sampled from existing datasets + exams (zh), then LLM augmentation (zh). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "CatQA", "link": "https://huggingface.co/datasets/declare-lab/CategoricalHarmfulQA", "all_links": ["https://huggingface.co/datasets/declare-lab/CategoricalHarmfulQA", "https://github.com/declare-lab/resta", "https://aclanthology.org/2024.acl-long.762/"], "description": "**550 prompts**. Each prompt is a question. CatQA was created to **evaluate effectiveness of safety training method**. The dataset languages are English, Chinese and Vietnamese. Dataset entries are **machine-written** : generated by unnamed LLM that is not safety-tuned. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "XSafety", "link": "https://github.com/Jarviswang94/Multilingual_safety_benchmark", "all_links": ["https://github.com/Jarviswang94/Multilingual_safety_benchmark", "https://aclanthology.org/2024.findings-acl.349/"], "description": "**28,000 prompts**. Each prompt is a question or instruction. XSafety was created to **evaluate multilingual LLM safety**. The dataset languages are English, Chinese, Hindi, Spanish, French, Arabic, Bengali, Russian, Japanese and German. Dataset entries were created in a **hybrid** fashion: sampled from SafetyPrompts, then auto-translated and validated. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SaladBench", "link": "https://huggingface.co/datasets/OpenSafetyLab/Salad-Data", "all_links": ["https://huggingface.co/datasets/OpenSafetyLab/Salad-Data", "https://github.com/OpenSafetyLab/SALAD-BENCH", "https://arxiv.org/abs/2402.05044"], "description": "**21,000 prompts**. Each prompt is a question or instruction. SaladBench was created to **evaluate LLM safety, plus attack and defense methods**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: mostly sampled from existing datasets, then augmented using GPT-4. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "GPTFuzzer", "link": "https://github.com/sherdencooper/GPTFuzz", "all_links": ["https://github.com/sherdencooper/GPTFuzz", "https://arxiv.org/abs/2309.10253"], "description": "**100 prompts**. Each prompt is a question or instruction. GPTFuzzer was created to **evaluate effectiveness of automated red-teaming method**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AnthropicHarmlessBase and unpublished GPT-written dataset. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ALERT", "link": "https://huggingface.co/datasets/Babelscape/ALERT", "all_links": ["https://huggingface.co/datasets/Babelscape/ALERT", "https://github.com/Babelscape/ALERT", "https://arxiv.org/abs/2404.08676"], "description": "**44,800 prompts**. Each prompt is a question or instruction. ALERT was created to **evaluate the safety of LLMs through red teaming methodologies**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AnthropicRedTeam, then augmented with templates. The dataset license is CC BY-NC-SA 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "Sorry<PERSON><PERSON><PERSON>", "link": "https://huggingface.co/datasets/sorry-bench/sorry-bench-202406", "all_links": ["https://huggingface.co/datasets/sorry-bench/sorry-bench-202406", "https://github.com/sorry-bench/sorry-bench", "https://arxiv.org/abs/2406.14598"], "description": "**9,450 prompts**. Each prompt is an unsafe question or instruction. SorryBench was created to **evaluate fine-grained LLM safety across varying linguistic characteristics**. The dataset languages are English, French, Chinese, Marathi, Tamil and Malayalam. Dataset entries were created in a **hybrid** fashion: sampled from 10 other datasets, then augmented with templates. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "Flames", "link": "https://github.com/AIFlames/Flames", "all_links": ["https://github.com/AIFlames/Flames", "https://aclanthology.org/2024.naacl-long.256/"], "description": "**2,251 prompts**. Each prompt is a question or instruction. Flames was created to **evaluate value alignment of Chinese language LLMs**. The dataset language is Chinese. Dataset entries are **human-written** : written by crowdworkers. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SEval", "link": "https://huggingface.co/datasets/IS2Lab/S-Eval", "all_links": ["https://huggingface.co/datasets/IS2Lab/S-Eval", "https://github.com/IS2Lab/S-Eval", "https://arxiv.org/abs/2405.14191"], "description": "**20,000 prompts**. Each prompt is an unsafe question or instruction. SEval was created to **evaluate LLM safety**. The dataset languages are English and Chinese. Dataset entries are **machine-written** : generated by a fine-tuned Qwen-14b. The dataset license is CC BY-NC-SA 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SAFE", "link": "https://github.com/xiaoqiao/EvalSafetyLLM", "all_links": ["https://github.com/xiaoqiao/EvalSafetyLLM", "https://ieeexplore.ieee.org/abstract/document/10507773"], "description": "**52,430 conversations**. Each conversation is single-turn, containing a prompt and a potentially harmful model response. SAFE was created to **evaluate LLM safety beyond binary distincton of safe and unsafe**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled seed prompts from Friday website, then generated more prompts with GPT-4. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "DoNotAnswer", "link": "https://huggingface.co/datasets/LibrAI/do-not-answer", "all_links": ["https://huggingface.co/datasets/LibrAI/do-not-answer", "https://github.com/Libr-AI/do-not-answer", "https://aclanthology.org/2024.findings-eacl.61/"], "description": "**939 prompts**. Each prompt is a question. DoNotAnswer was created to **evaluate 'dangerous capabilities' of LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by GPT-4. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "<PERSON>rmBen<PERSON>", "link": "https://github.com/centerforaisafety/HarmBench/tree/main/data/behavior_datasets", "all_links": ["https://github.com/centerforaisafety/HarmBench/tree/main/data/behavior_datasets", "https://arxiv.org/abs/2402.04249"], "description": "**400 prompts**. Each prompt is an instruction. HarmBench was created to **evaluate effectiveness of automated red-teaming methods**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "DecodingTrust", "link": "https://huggingface.co/datasets/AI-Secure/DecodingTrust", "all_links": ["https://huggingface.co/datasets/AI-Secure/DecodingTrust", "https://github.com/AI-secure/DecodingTrust", "https://arxiv.org/abs/2306.11698"], "description": "**243,877 prompts**. Each prompt is an instruction. DecodingTrust was created to **evaluate trustworthiness of LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates and examples plus extensive augmentation from GPTs. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SimpleSafetyTests", "link": "https://huggingface.co/datasets/Bertievidgen/SimpleSafetyTests", "all_links": ["https://huggingface.co/datasets/Bertievidgen/SimpleSafetyTests", "https://github.com/bertiev/SimpleSafetyTests", "https://arxiv.org/abs/2311.08370"], "description": "**100 prompts**. Each prompt is a simple question or instruction. SimpleSafetyTests was created to **evaluate critical safety risks in LLMs**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "MaliciousInstruct", "link": "https://github.com/Princeton-SysML/Jailbreak_LLM/tree/main/data", "all_links": ["https://github.com/Princeton-SysML/Jailbreak_LLM/tree/main/data", "https://openreview.net/forum?id=r42tSSCHPh"], "description": "**100 prompts**. Each prompt is an unsafe question. MaliciousInstruct was created to **evaluate success of generation exploit jailbreak**. The dataset language is English. Dataset entries are **machine-written** : written by ChatGPT, then filtered by authors. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "HExPHI", "link": "https://huggingface.co/datasets/LLM-Tuning-Safety/HEx-PHI", "all_links": ["https://huggingface.co/datasets/LLM-Tuning-Safety/HEx-PHI", "https://openreview.net/forum?id=hTEGyKf0dZ"], "description": "**330 prompts**. Each prompt is a harmful instruction. HExPHI was created to **evaluate LLM safety**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AdvBench and AnthropicRedTeam, then refined manually and with LLMs.. The dataset license is custom (HEx-PHI). Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "QHarm", "link": "https://github.com/vinid/instruction-llms-safety-eval", "all_links": ["https://github.com/vinid/instruction-llms-safety-eval", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**100 prompts**. Each prompt is a question. QHarm was created to **evaluate LLM safety**. The dataset language is English. Dataset entries are **human-written** : sampled randomly from AnthropicHarmlessBase (written by crowdworkers). The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "MaliciousInstructions", "link": "https://github.com/vinid/instruction-llms-safety-eval", "all_links": ["https://github.com/vinid/instruction-llms-safety-eval", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**100 prompts**. Each prompt is an instruction. MaliciousInstructions was created to **evaluate compliance of LLMs with malicious instructions**. The dataset language is English. Dataset entries are **machine-written** : generated by GPT-3 (text-davinci-003). The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SafetyInstructions", "link": "https://github.com/vinid/safety-tuned-llamas/tree/main/data/training", "all_links": ["https://github.com/vinid/safety-tuned-llamas/tree/main/data/training", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**2,000 conversations**. Each conversation is a user prompt with a safe model response. SafetyInstructions was created to **fine-tune LLMs to be safer**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: prompts sampled form AnthropicRedTeam, responses generated by gpt-3.5-turbo. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AdvBench", "link": "https://github.com/llm-attacks/llm-attacks/tree/main/data/advbench", "all_links": ["https://github.com/llm-attacks/llm-attacks/tree/main/data/advbench", "https://arxiv.org/abs/2307.15043"], "description": "**1,000 prompts**. 500 are harmful strings that the model should not reproduce, 500 are harmful instructions. AdvBench was created to **elicit generation of harmful or objectionable content from LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by Wizard-Vicuna-30B-Uncensored. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "BeaverTails", "link": "https://huggingface.co/datasets/PKU-Alignment/BeaverTails", "all_links": ["https://huggingface.co/datasets/PKU-Alignment/BeaverTails", "https://github.com/PKU-Alignment/beavertails", "https://arxiv.org/abs/2307.04657"], "description": "**333,963 conversations**. Each conversation contains a human prompt and LLM response. BeaverTails was created to **evaluate and improve LLM safety on QA**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: prompts sampled from human AnthropicRedTeam data, plus model-generated responses. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "TDCRedTeaming", "link": "https://github.com/centerforaisafety/tdc2023-starter-kit/tree/main/red_teaming/data", "all_links": ["https://github.com/centerforaisafety/tdc2023-starter-kit/tree/main/red_teaming/data", "https://trojandetection.ai/"], "description": "**100 prompts**. Each prompt is an instruction. TDCRedTeaming was created to **evaluate success of automated red-teaming approaches**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "JADE", "link": "https://github.com/whitzard-ai/jade-db", "all_links": ["https://github.com/whitzard-ai/jade-db", "https://arxiv.org/abs/2311.00286"], "description": "**2,130 prompts**. Each prompt is a question targeting a specific harm category. JADE was created to **use linguistic fuzzing to generate challenging prompts for evaluating LLM safety**. The dataset languages are Chinese and English. Dataset entries are **machine-written** : generated by LLMs based on linguistic rules. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "CPAD", "link": "https://github.com/liuchengyuan123/CPAD", "all_links": ["https://github.com/liuchengyuan123/CPAD", "https://arxiv.org/abs/2309.11830"], "description": "**10,050 prompts**. Each prompt is a longer-form scenario / instruction aimed at eliciting a harmful response. CPAD was created to **elicit generation of harmful or objectionable content from LLMs**. The dataset language is Chinese. Dataset entries were created in a **hybrid** fashion: mostly generated by GPTs based on some human seed prompts. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AdvPromptSet", "link": "https://github.com/facebookresearch/ResponsibleNLP/tree/main/AdvPromptSet", "all_links": ["https://github.com/facebookresearch/ResponsibleNLP/tree/main/AdvPromptSet", "https://aclanthology.org/2023.emnlp-main.230/"], "description": "**197,628 sentences**. Each sentence is taken from a social media dataset. AdvPromptSet was created to **evaluate LLM responses to adversarial toxicity text prompts**. The dataset language is English. Dataset entries are **human-written** : sampled from two Jigsaw social media datasets (written by social media users). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AART", "link": "https://github.com/google-research-datasets/aart-ai-safety-dataset", "all_links": ["https://github.com/google-research-datasets/aart-ai-safety-dataset", "https://aclanthology.org/2023.emnlp-industry.37/"], "description": "**3,269 prompts**. Each prompt is an instruction. AART was created to **illustrate the AART automated red-teaming method**. The dataset language is English. Dataset entries are **machine-written** : generated by PALM. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "DELPHI", "link": "https://github.com/apple/ml-delphi/tree/main/dataset", "all_links": ["https://github.com/apple/ml-delphi/tree/main/dataset", "https://aclanthology.org/2023.emnlp-industry.76"], "description": "**29,201 prompts**. Each prompt is a question about a more or less controversial issue. DELPHI was created to **evaluate LLM performance in handling controversial issues**. The dataset language is English. Dataset entries are **human-written** : sampled from the Quora Question Pair Dataset (written by Quora users). The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AttaQ", "link": "https://huggingface.co/datasets/ibm/AttaQ", "all_links": ["https://huggingface.co/datasets/ibm/AttaQ", "https://aclanthology.org/2023.gem-1.10/"], "description": "**1,402 prompts**. Each prompt is a question. AttaQ was created to **evaluate tendency of LLMs to generate harmful or undesirable responses**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AnthropicRedTeam, and LLM-generated, and crawled from Wikipedia. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "FFT", "link": "https://github.com/cuishiyao96/FFT", "all_links": ["https://github.com/cuishiyao96/FFT", "https://arxiv.org/abs/2311.18580"], "description": "**2,116 prompts**. Each prompt is a question or instruction, sometimes within a jailbreak template. FFT was created to **evaluation factuality, fairness, and toxicity of LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from AnthropicRedTeam, and LLM-generated, and crawled from Wikipedia, Reddit and elsewhere. The dataset license is not specified. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "HarmfulQA", "link": "https://huggingface.co/datasets/declare-lab/HarmfulQA", "all_links": ["https://huggingface.co/datasets/declare-lab/HarmfulQA", "https://github.com/declare-lab/red-instruct/tree/main/harmful_questions", "https://arxiv.org/abs/2308.09662"], "description": "**1,960 prompts**. Each prompt is a question. HarmfulQA was created to **evaluate and improve LLM safety**. The dataset language is English. Dataset entries are **machine-written** : generated by ChatGPT. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "HarmfulQ", "link": "https://github.com/SALT-NLP/chain-of-thought-bias", "all_links": ["https://github.com/SALT-NLP/chain-of-thought-bias", "https://aclanthology.org/2023.acl-long.244/"], "description": "**200 prompts**. Each prompt is a question. HarmfulQ was created to **evaluate LLM safety**. The dataset language is English. Dataset entries are **machine-written** : generated by GPT-3 (text-davinci-002). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ModelWrittenAdvancedAIRisk", "link": "https://huggingface.co/datasets/Anthropic/model-written-evals", "all_links": ["https://huggingface.co/datasets/Anthropic/model-written-evals", "https://github.com/anthropics/evals/tree/main/advanced-ai-risk", "https://aclanthology.org/2023.findings-acl.847/"], "description": "**24,516 binary-choice questions**. ModelWrittenAdvancedAIRisk was created to **evaluate advanced AI risk posed by LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: generated by an unnamed LLM and crowdworkers. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SafetyPrompts", "link": "https://huggingface.co/datasets/thu-coai/Safety-Prompts", "all_links": ["https://huggingface.co/datasets/thu-coai/Safety-Prompts", "https://github.com/thu-coai/Safety-Prompts", "https://arxiv.org/abs/2304.10436"], "description": "**100,000 prompts**. Each prompt is a question or instruction. SafetyPrompts was created to **evaluate the safety of Chinese LLMs**. The dataset language is Chinese. Dataset entries were created in a **hybrid** fashion: human-written examples, augmented by LLMs. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ProsocialDialog", "link": "https://huggingface.co/datasets/allenai/prosocial-dialog", "all_links": ["https://huggingface.co/datasets/allenai/prosocial-dialog", "https://github.com/skywalker023/prosocial-dialog", "https://aclanthology.org/2022.emnlp-main.267/"], "description": "**58,137 conversations**. Each conversation starts with a potentially unsafe opening followed by constructive feedback. ProsocialDialog was created to **teach conversational agents to respond to problematic content following social norms**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: GPT3-written openings with US crowdworker responses. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AnthropicRedTeam", "link": "https://huggingface.co/datasets/Anthropic/hh-rlhf", "all_links": ["https://huggingface.co/datasets/Anthropic/hh-rlhf", "https://github.com/anthropics/hh-rlhf", "https://arxiv.org/abs/2209.07858"], "description": "**38,961 conversations**. Conversations can be multi-turn, with user input and LLM output. AnthropicRedTeam was created to **analyse how people red-team LLMs**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers (Upwork + MTurk). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SaFeRDialogues", "link": "https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/saferdialogues", "all_links": ["https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/saferdialogues", "https://aclanthology.org/2022.acl-long.447/"], "description": "**7,881 conversations**. Each conversation contains a safety failure plus a recovery response. SaFeRDialogues was created to **recovering from safety failures in LLM conversations**. The dataset language is English. Dataset entries are **human-written** : unsafe conversation starters sampled from BAD, recovery responses written by crowdworkers. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "SafetyKit", "link": "https://github.com/facebookresearch/ParlAI/tree/main/projects/safety_bench", "all_links": ["https://github.com/facebookresearch/ParlAI/tree/main/projects/safety_bench", "https://aclanthology.org/2022.acl-long.284/"], "description": "**990 prompts**. Each prompt is a question, instruction or statement. SafetyKit was created to **quickly assess apparent safety concerns in conversational AI**. The dataset language is English. Dataset entries are **human-written** : sampled from several human-written datasets. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "DiaSafety", "link": "https://huggingface.co/datasets/thu-coai/diasafety", "all_links": ["https://huggingface.co/datasets/thu-coai/diasafety", "https://github.com/thu-coai/DiaSafety", "https://aclanthology.org/2022.findings-acl.308/"], "description": "**11,492 conversational turns**. Each turn consists of a context and a response. DiaSafety was created to **capture unsafe behaviors in humanbot dialogue settings**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: sampled from Reddit, other datasets, and machine-generated. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "AnthropicHarmlessBase", "link": "https://huggingface.co/datasets/Trelis/hh-rlhf-dpo", "all_links": ["https://huggingface.co/datasets/Trelis/hh-rlhf-dpo", "https://github.com/anthropics/hh-rlhf", "https://arxiv.org/abs/2204.05862"], "description": "**44,849 conversational turns**. Each turn consists of a user prompt and multiple LLM completions. AnthropicHarmlessBase was created to **red-team LLMs**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers (Upwork + MTurk). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "BAD", "link": "https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/bot_adversarial_dialogue", "all_links": ["https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/bot_adversarial_dialogue", "https://aclanthology.org/2021.naacl-main.235"], "description": "**78,874 conversations**. Conversations can be multi-turn, with user input and LLM output. BAD was created to **evaluate the safety of conversational agents**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers with the goal of making models give unsafe responses, and also validated by multiple annotators. The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "RealToxicityPrompts", "link": "https://huggingface.co/datasets/allenai/real-toxicity-prompts", "all_links": ["https://huggingface.co/datasets/allenai/real-toxicity-prompts", "https://github.com/allenai/real-toxicity-prompts", "https://aclanthology.org/2020.findings-emnlp.301/"], "description": "**99,442 prompts**. Each prompt is an unfinished sentence from the OpenWebCorpus. RealToxicityPrompts was created to **evaluate propensity of LLMs to generate toxic content**. The dataset language is English. Dataset entries are **human-written** : sampled OpenWebCorpus sentences. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "ParlAIDialogueSafety", "link": "https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/dialogue_safety", "all_links": ["https://github.com/facebookresearch/ParlAI/tree/main/parlai/tasks/dialogue_safety", "https://aclanthology.org/D19-1461"], "description": "**90,000 prompts**. 30k are for multi-turn tasks, and 60k for single-turn tasks. ParlAIDialogueSafety was created to **evaluate and improve the safety of conversational agents**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers in isolation (\"standard\"), or with the goal of making a model give an offensive response (\"adversarial\"). The dataset license is MIT. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "EmpatheticDialogues", "link": "https://huggingface.co/datasets/empathetic_dialogues", "all_links": ["https://huggingface.co/datasets/empathetic_dialogues", "https://github.com/facebookresearch/EmpatheticDialogues", "https://aclanthology.org/P19-1534"], "description": "**24,850 conversations**. Each conversation is about an emotional situation described by one speaker, across one or multiple turns. EmpatheticDialogues was created to **train dialogue agents to be more empathetic**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers (US MTurkers). The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Broad Safety Datasets"}, {"dataset_name": "MedSafetyBench", "link": "https://github.com/AI4LIFE-GROUP/med-safety-bench", "all_links": ["https://github.com/AI4LIFE-GROUP/med-safety-bench", "https://arxiv.org/abs/2403.03744"], "description": "**1,800 conversations**. Each conversation is an unsafe medical request with an associated safe response. MedSafetyBench was created to **measure the medical safety of LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by GPT-4 and llama-2-7b. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "CoCoNot", "link": "https://huggingface.co/datasets/allenai/coconot", "all_links": ["https://huggingface.co/datasets/allenai/coconot", "https://github.com/allenai/noncompliance", "https://arxiv.org/abs/2407.12043"], "description": "**12,478 prompts**. Each prompt is a question or instruction that models should not comply with. CoCoNot was created to **evaluate and improve contextual non-compliance in LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written seed prompts expanded using LLMs. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "DoAnythingNow", "link": "https://github.com/verazuo/jailbreak_llms", "all_links": ["https://github.com/verazuo/jailbreak_llms", "https://dl.acm.org/doi/10.1145/3658644.3670388"], "description": "**15,140 prompts**. Each prompt is an instruction or question, sometimes with a jailbreak. DoAnythingNow was created to **characterise and evaluate in-the-wild LLM jailbreak prompts**. The dataset language is English. Dataset entries are **human-written** : written by users on Reddit, Discord, websites and in other datasets. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "SGXSTest", "link": "https://huggingface.co/datasets/walledai/SGXSTest", "all_links": ["https://huggingface.co/datasets/walledai/SGXSTest", "https://github.com/walledai/walledeval", "https://aclanthology.org/2024.emnlp-demo.42/"], "description": "**200 prompts**. Each prompt is a simple question. SGXSTest was created to **evaluate exaggerated safety / false refusal in LLMs for the Singaporean context**. The dataset language is English. Dataset entries are **human-written** : created based on XSTest. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "HiXSTest", "link": "https://huggingface.co/datasets/walledai/HiXSTest", "all_links": ["https://huggingface.co/datasets/walledai/HiXSTest", "https://github.com/walledai/walledeval", "https://aclanthology.org/2024.emnlp-demo.42/"], "description": "**50 prompts**. Each prompt is a simple question. HiXSTest was created to **evaluate exaggerated safety / false refusal behaviour in Hindi LLMs**. The dataset language is Hindi. Dataset entries are **human-written** : created based on XSTest. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PHTest", "link": "https://huggingface.co/datasets/furonghuang-lab/PHTest", "all_links": ["https://huggingface.co/datasets/furonghuang-lab/PHTest", "https://github.com/umd-huang-lab/FalseRefusal", "https://arxiv.org/abs/2409.00598"], "description": "**3,260 prompts**. Each prompt is a \"pseudo-harmful\" question or instruction. PHTest was created to **evaluate false refusal in LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by Llama2-7B-Chat, Llama3-8B-Instruct, and Mistral-7B-Instruct-V0.2.. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "WMDP", "link": "https://huggingface.co/datasets/cais/wmdp", "all_links": ["https://huggingface.co/datasets/cais/wmdp", "https://github.com/centerforaisafety/wmdp", "https://proceedings.mlr.press/v235/li24bc.html"], "description": "**3,688 multiple-choice questions**. WMDP was created to **measure hazardous knowledge in biosecurity, cybersecurity, and chemical security**. The dataset language is English. Dataset entries are **human-written** : written by experts (academics and technical consultants). The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "ORBench", "link": "https://huggingface.co/datasets/bench-llm/or-bench", "all_links": ["https://huggingface.co/datasets/bench-llm/or-bench", "https://github.com/justincui03/or-bench", "https://arxiv.org/abs/2405.20947"], "description": "**80,000 prompts**. Each prompt is a question or instruction. ORBench was created to **evaluate false refusal in LLMs at scale**. The dataset language is English. Dataset entries are **machine-written** : generated by Mixtral as toxic prompts, then rewritten by <PERSON><PERSON> into safe prompts, then filtered. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "XSTest", "link": "https://huggingface.co/datasets/natolambert/xstest-v2-copy", "all_links": ["https://huggingface.co/datasets/natolambert/xstest-v2-copy", "https://github.com/paul-rottger/exaggerated-safety", "https://aclanthology.org/2024.naacl-long.301/"], "description": "**450 prompts**. Each prompt is a simple question. XSTest was created to **evaluate exaggerated safety / false refusal behaviour in LLMs**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "RuLES", "link": "https://huggingface.co/datasets/normster/RuLES", "all_links": ["https://huggingface.co/datasets/normster/RuLES", "https://github.com/normster/llm_rules", "https://arxiv.org/abs/2311.04235"], "description": "**862 prompts**. Each prompt is a test case combining rules and instructions. RuLES was created to **evaluate the ability of LLMs to follow simple rules**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "ConfAIde", "link": "https://github.com/skywalker023/confAIde/tree/main/benchmark", "all_links": ["https://github.com/skywalker023/confAIde/tree/main/benchmark", "https://openreview.net/forum?id=gmg7t8b4s0"], "description": "**1,326 prompts**. Each prompt is a reasoning task, with complexity increasing across 4 tiers. Answer formats vary. ConfAIde was created to **evaluate the privacy-reasoning capabilities of instruction-tuned LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination and with LLMs. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "CoNA", "link": "https://github.com/vinid/instruction-llms-safety-eval", "all_links": ["https://github.com/vinid/instruction-llms-safety-eval", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**178 prompts**. Each prompt is an instruction. CoNA was created to **evaluate compliance of LLMs with harmful instructions**. The dataset language is English. Dataset entries are **human-written** : sampled from MT-CONAN, then rephrased. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "ControversialInstructions", "link": "https://github.com/vinid/instruction-llms-safety-eval", "all_links": ["https://github.com/vinid/instruction-llms-safety-eval", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**40 prompts**. Each prompt is an instruction. ControversialInstructions was created to **evaluate LLM behaviour on controversial topics**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PhysicalSafetyInstructions", "link": "https://github.com/vinid/instruction-llms-safety-eval", "all_links": ["https://github.com/vinid/instruction-llms-safety-eval", "https://openreview.net/forum?id=gT5hALch9z"], "description": "**1,000 prompts**. Each prompt is an instruction. PhysicalSafetyInstructions was created to **evaluate LLM commonsense physical safety**. The dataset language is English. Dataset entries are **human-written** : sampled from SafeText, then rephrased. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "SycophancyEval", "link": "https://huggingface.co/datasets/meg-tong/sycophancy-eval", "all_links": ["https://huggingface.co/datasets/meg-tong/sycophancy-eval", "https://github.com/meg-tong/sycophancy-eval/tree/main", "https://openreview.net/forum?id=tvhaxkMKAn"], "description": "**20,956 prompts**. Each prompt is an open-ended question or instruction. SycophancyEval was created to **evaluate sycophancy in LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: prompts are written by humans and models. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "OKTest", "link": "https://github.com/InvokerStark/OverKill", "all_links": ["https://github.com/InvokerStark/OverKill", "https://arxiv.org/abs/2401.17633"], "description": "**350 prompts**. Each prompt is a question or instruction that models should not refuse. OKTest was created to **evaluate false refusal in LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by GPT-4 based on keywords. The dataset license is not specified. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PromptExtractionRobustness", "link": "https://huggingface.co/datasets/qxcv/tensor-trust?row=0", "all_links": ["https://huggingface.co/datasets/qxcv/tensor-trust?row=0", "https://github.com/HumanCompatibleAI/tensor-trust-data", "https://openreview.net/forum?id=UWymGURI75"], "description": "**569 samples**. Each sample combines defense and attacker input. PromptExtractionRobustness was created to **evaluate LLM vulnerability to prompt extraction**. The dataset language is English. Dataset entries are **human-written** : written by Tensor Trust players. The dataset license is not specified. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PromptHijackingRobustness", "link": "https://huggingface.co/datasets/qxcv/tensor-trust?row=0", "all_links": ["https://huggingface.co/datasets/qxcv/tensor-trust?row=0", "https://github.com/HumanCompatibleAI/tensor-trust-data", "https://openreview.net/forum?id=UWymGURI75"], "description": "**775 samples**. Each sample combines defense and attacker input. PromptHijackingRobustness was created to **evaluate LLM vulnerability to prompt hijacking**. The dataset language is English. Dataset entries are **human-written** : written by Tensor Trust players. The dataset license is not specified. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "CyberattackAssistance", "link": "https://github.com/facebookresearch/PurpleLlama/tree/main/CybersecurityBenchmarks/datasets/mitre", "all_links": ["https://github.com/facebookresearch/PurpleLlama/tree/main/CybersecurityBenchmarks/datasets/mitre", "https://arxiv.org/abs/2312.04724"], "description": "**1,000 prompts**. Each prompt is an instruction to assist in a cyberattack. CyberattackAssistance was created to **evaluate LLM compliance in assisting in cyberattacks**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: written by experts, augmented with LLMs. The dataset license is custom (Llama2 Community License). Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "SPMisconceptions", "link": "https://github.com/purseclab/LLM_Security_Privacy_Advice", "all_links": ["https://github.com/purseclab/LLM_Security_Privacy_Advice", "https://dl.acm.org/doi/10.1145/3627106.3627196"], "description": "**122 prompts**. Each prompt is a single-sentence misconception. SPMisconceptions was created to **measure the ability of LLMs to refute misconceptions**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "LatentJailbreak", "link": "https://github.com/qiuhuachuan/latent-jailbreak/tree/main", "all_links": ["https://github.com/qiuhuachuan/latent-jailbreak/tree/main", "https://arxiv.org/abs/2307.08487"], "description": "**416 prompts**. Each prompt combines a meta-instruction (e.g. translation) with the same toxic instruction template. LatentJailbreak was created to **evaluate safety and robustness of LLMs in response to adversarial prompts**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "ModelWrittenSycophancy", "link": "https://huggingface.co/datasets/Anthropic/model-written-evals", "all_links": ["https://huggingface.co/datasets/Anthropic/model-written-evals", "https://github.com/anthropics/evals/tree/main/sycophancy", "https://aclanthology.org/2023.findings-acl.847/"], "description": "**30,051 binary-choice questions**. ModelWrittenSycophancy was created to **evaluate sycophancy in LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: questions sampled from surveys with contexts generated by an unnamed LLM. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "SafeText", "link": "https://github.com/sharonlevy/SafeText", "all_links": ["https://github.com/sharonlevy/SafeText", "https://aclanthology.org/2022.emnlp-main.154/"], "description": "**367 prompts**. Prompts are combined with 1,465 commands to create pieces of advice. SafeText was created to **evaluate commonsense physical safety**. The dataset language is English. Dataset entries are **human-written** : written by Reddit users, posts sampled with multiple filtering steps. The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PersonalInfoLeak", "link": "https://github.com/jeffhj/LM_PersonalInfoLeak", "all_links": ["https://github.com/jeffhj/LM_PersonalInfoLeak", "https://aclanthology.org/2022.findings-emnlp.148"], "description": "**3,238 entries**. Each entry is a tuple of name and email address. PersonalInfoLeak was created to **evaluate whether LLMs are prone to leaking PII**. The dataset language is English. Dataset entries are **human-written** : sampled from Enron email corpus. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "ToxiGen", "link": "https://huggingface.co/datasets/skg/toxigen-data", "all_links": ["https://huggingface.co/datasets/skg/toxigen-data", "https://github.com/microsoft/TOXIGEN", "https://aclanthology.org/2022.acl-long.234/"], "description": "**260,851 prompts**. Each prompt comprises n-shot examples of toxic content. ToxiGen was created to **create new examples of implicit hate speech**. The dataset language is English. Dataset entries are **human-written** : sampled from Gab and Reddit (hate), news and blogs (not hate). The dataset license is MIT. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "TruthfulQA", "link": "https://huggingface.co/datasets/truthful_qa", "all_links": ["https://huggingface.co/datasets/truthful_qa", "https://github.com/sylinrl/TruthfulQA", "https://aclanthology.org/2022.acl-long.229/"], "description": "**817 prompts**. Each prompt is a question. TruthfulQA was created to **evaluate truthfulness in LLM answers**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Narrow Safety Datasets"}, {"dataset_name": "PRISM", "link": "https://huggingface.co/datasets/HannahRoseKirk/prism-alignment", "all_links": ["https://huggingface.co/datasets/HannahRoseKirk/prism-alignment", "https://github.com/HannahKirk/prism-alignment", "https://arxiv.org/abs/2404.16019"], "description": "**8,011 conversations**. Conversations can be multi-turn, with user input and responses from one or multiple LLMs. PRISM was created to **capture diversity of human preferences over LLM behaviours**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "MultiTP", "link": "https://github.com/causalNLP/moralmachine", "all_links": ["https://github.com/causalNLP/moralmachine", "https://arxiv.org/abs/2407.02273"], "description": "**107,000 binary-choice questions**. Each question is a trolley-style moral choice scenario. MultiTP was created to **evaluate LLM moral decision-making across many languages**. The dataset languages are Afrikaans, Amharic, Arabic, Azerbaijani, Belarusian, Bulgarian, Bengali, Bosnian, Catalan, Cebuano, Corsican, Czech, Welsh, Danish, German, Modern Greek, English, Esperanto, Spanish, Estonian, Basque, Persian, Finnish, French, Western Frisian, Irish, Scottish Gaelic, Galician, Gujarati, Hausa, Hawaiian, Hebrew, Hindi, Hmong, Croatian, Haitian, Hungarian, Armenian, Indonesian, Igbo, Icelandic, Italian, Modern Hebrew, Japanese, Javanese, Georgian, Kazakh, Central Khmer, Kannada, Korean, Kurdish, Kirghiz, Latin, Luxembourgish, Lao, Lithuanian, Latvian, Malagasy, Maori, Macedonian, Malayalam, Mongolian, Marathi, Malay, Maltese, Burmese, Nepali, Dutch, Norwegian, Nyanja, Oriya, Panjabi, Polish, Pushto, Portuguese, Romanian, Russian, Sindhi, Sinhala, Slovak, Slovenian, Samoan, Shona, Somali, Albanian, Serbian, Southern Sotho, Sundanese, Swedish, Swahili, Tamil, Telugu, Tajik, Thai, Tagalog, Turkish, Uighur, Ukrainian, Urdu, Uzbek, Vietnamese, Xhosa, Yiddish, Yoruba, Chinese, Chinese (Traditional) and Zulu. Dataset entries were created in a **hybrid** fashion: sampled from Moral Machines, then expanded with templated augmentations, then auto-translated into 106 languages. The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "CIVICS", "link": "https://huggingface.co/datasets/CIVICS-dataset/CIVICS", "all_links": ["https://huggingface.co/datasets/CIVICS-dataset/CIVICS", "https://ojs.aaai.org/index.php/AIES/article/view/31710"], "description": "**699 excerpts**. Each excerpt is a short value-laden text passage. CIVICS was created to **evaluate the social and cultural variation of LLMs across multiple languages and value-sensitive topics**. The dataset languages are German, Italian, French and English. Dataset entries are **human-written** : sampled from government and media sites. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "GlobalOpinionQA", "link": "https://huggingface.co/datasets/Anthropic/llm_global_opinions", "all_links": ["https://huggingface.co/datasets/Anthropic/llm_global_opinions", "https://arxiv.org/abs/2306.16388"], "description": "**2,556 multiple-choice questions**. GlobalOpinionQA was created to **evaluate whose opinions LLM responses are most similar to**. The dataset language is English. Dataset entries are **human-written** : adapted from Pew’s Global Attitudes surveys and the World Values Survey (written by survey designers). The dataset license is CC BY-NC-SA 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "KorNAT", "link": "https://huggingface.co/datasets/datumo/KorNAT", "all_links": ["https://huggingface.co/datasets/datumo/KorNAT", "https://aclanthology.org/2024.findings-acl.666"], "description": "**10,000 multiple-choice questions**. Each question is about a specific social scenario. KorNAT was created to **evaluate LLM alignment with South Korean social values**. The dataset languages are English and Korean. Dataset entries were created in a **hybrid** fashion: generated by GPT-3.5 based on keywords sampled from media and surveys. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "C<PERSON><PERSON><PERSON><PERSON>", "link": "https://github.com/tjunlp-lab/CMoralEval", "all_links": ["https://github.com/tjunlp-lab/CMoralEval", "https://aclanthology.org/2024.findings-acl.703"], "description": "**30,388 multiple-choice questions**. Each question is about a specific moral scenario. CMoralEval was created to **evaluate the morality of Chinese LLMs**. The dataset language is Chinese. Dataset entries were created in a **hybrid** fashion: sampled from Chinese TV programs and other media, then turned into templates using GPT 3.5. The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "WorldValuesBench", "link": "https://github.com/Demon702/WorldValuesBench", "all_links": ["https://github.com/Demon702/WorldValuesBench", "https://aclanthology.org/2024.lrec-main.1539/"], "description": "**21,492,393 multiple-choice questions**. Each question comes with sociodemographic attributes of the respondent. WorldValuesBench was created to **evaluate LLM awareness of multicultural human values**. The dataset language is English. Dataset entries are **human-written** : adapted from the World Values Survey (written by survey designers) using templates. The dataset license is not specified. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "MoralChoice", "link": "https://huggingface.co/datasets/ninoscherrer/moralchoice", "all_links": ["https://huggingface.co/datasets/ninoscherrer/moralchoice", "https://github.com/ninodimontalcino/moralchoice", "https://openreview.net/forum?id=O06z2G18me"], "description": "**1,767 binary-choice question**. Each prompt is a hypothetical moral scenario with two potential actions. MoralChoice was created to **evaluate the moral beliefs in LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: mostly generated by GPTs, plus some human-written scenarios. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "DICES350", "link": "https://github.com/google-research-datasets/dices-dataset/", "all_links": ["https://github.com/google-research-datasets/dices-dataset/", "https://proceedings.neurips.cc/paper_files/paper/2023/hash/a74b697bce4cac6c91896372abaa8863-Abstract-Datasets_and_Benchmarks.html"], "description": "**350 conversations**. Conversations can be multi-turn, with user input and LLM output. DICES350 was created to **collect diverse perspectives on conversational AI safety**. The dataset language is English. Dataset entries are **human-written** : written by adversarial Lamda users. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "DICES990", "link": "https://github.com/google-research-datasets/dices-dataset/", "all_links": ["https://github.com/google-research-datasets/dices-dataset/", "https://proceedings.neurips.cc/paper_files/paper/2023/hash/a74b697bce4cac6c91896372abaa8863-Abstract-Datasets_and_Benchmarks.html"], "description": "**990 conversations**. Conversations can be multi-turn, with user input and LLM output. DICES990 was created to **collect diverse perspectives on conversational AI safety**. The dataset language is English. Dataset entries are **human-written** : written by adversarial Lamda users. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "OpinionQA", "link": "https://github.com/tatsu-lab/opinions_qa", "all_links": ["https://github.com/tatsu-lab/opinions_qa", "https://dl.acm.org/doi/10.5555/3618408.3619652"], "description": "**1,498 multiple-choice questions**. OpinionQA was created to **evaluate the alignment of LLM opinions with US demographic groups**. The dataset language is English. Dataset entries are **human-written** : adapted from the Pew American Trends Panel surveys. The dataset license is not specified. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "CValuesResponsibilityMC", "link": "https://huggingface.co/datasets/Skepsun/cvalues_rlhf", "all_links": ["https://huggingface.co/datasets/Skepsun/cvalues_rlhf", "https://github.com/X-PLUG/CValues", "https://arxiv.org/abs/2307.09705"], "description": "**1,712 multiple-choice questions**. Each question targets responsible behaviours. CValuesResponsibilityMC was created to **evaluate human value alignment in Chinese LLMs**. The dataset language is Chinese. Dataset entries are **machine-written** : automatically created from human-written prompts. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "CValuesResponsibilityPrompts", "link": "https://huggingface.co/datasets/Skepsun/cvalues_rlhf", "all_links": ["https://huggingface.co/datasets/Skepsun/cvalues_rlhf", "https://github.com/X-PLUG/CValues", "https://arxiv.org/abs/2307.09705"], "description": "**800 prompts**. Each prompt is an open question targeting responsible behaviours. CValuesResponsibilityPrompts was created to **evaluate human value alignment in Chinese LLMs**. The dataset language is Chinese. Dataset entries are **human-written** : written by the authors. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://huggingface.co/datasets/Anthropic/model-written-evals", "all_links": ["https://huggingface.co/datasets/Anthropic/model-written-evals", "https://github.com/anthropics/evals/tree/main/persona", "https://aclanthology.org/2023.findings-acl.847/"], "description": "**133,204 binary-choice questions**. ModelWrittenPersona was created to **evaluate LLM behaviour related to their stated political and religious views, personality, moral beliefs, and desire to pursue potentially dangerous goals**. The dataset language is English. Dataset entries are **machine-written** : generated by an unnamed LLM. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://github.com/aypan17/machiavelli", "all_links": ["https://github.com/aypan17/machiavelli", "https://openreview.net/forum?id=nkals4A4Vs"], "description": "**572,322 scenarios**. Each scenario is a choose-your-own-adventure style prompt. <PERSON><PERSON><PERSON><PERSON> was created to **evaluate ethical behaviour of LLM agents**. The dataset language is English. Dataset entries are **human-written** : human-written choose-your-own-adventure stories. The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "MoralExceptQA", "link": "https://huggingface.co/datasets/feradauto/MoralExceptQA", "all_links": ["https://huggingface.co/datasets/feradauto/MoralExceptQA", "https://github.com/feradauto/MoralCoT", "https://openreview.net/forum?id=uP9RiC4uVcR"], "description": "**148 prompts**. Each prompt is a question about a vignette (situation) related to a specific norm. MoralExceptQA was created to **evaluate LLM ability to understand, interpret and predict human moral judgments and decisions**. The dataset language is English. Dataset entries are **human-written** : written by the authors (?). The dataset license is not specified. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "MIC", "link": "https://github.com/SALT-NLP/mic", "all_links": ["https://github.com/SALT-NLP/mic", "https://aclanthology.org/2022.acl-long.261"], "description": "**38,000 conversations**. Each conversation is a single-turn with user input and LLM output. MIC was created to **understand the intuitions, values and moral judgments reflected in LLMs**. The dataset language is English. Dataset entries are **human-written** : questions sampled from AskReddit. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "<PERSON>iny<PERSON><PERSON>et", "link": "https://github.com/hendrycks/jiminy-cricket", "all_links": ["https://github.com/hendrycks/jiminy-cricket", "https://arxiv.org/abs/2110.13136"], "description": "**1,838 locations**. Each location comes with a number of actions to choose from. JiminyCricket was created to **evaluate alignment of agents with human values and morals**. The dataset language is English. Dataset entries are **human-written** : sampled from 25 text-based adventure games, then annotated for morality by human annotators. The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "MoralStories", "link": "https://huggingface.co/datasets/demelin/moral_stories", "all_links": ["https://huggingface.co/datasets/demelin/moral_stories", "https://github.com/demelin/moral_stories", "https://aclanthology.org/2021.emnlp-main.54/"], "description": "**12,000 stories**. Each story consists of seven sentences. MoralStories was created to **evaluate commonsense, moral and social reasoning skills of LLMs**. The dataset language is English. Dataset entries are **human-written** : written and validated by US MTurkers. The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "ETHICS", "link": "https://huggingface.co/datasets/hendrycks/ethics", "all_links": ["https://huggingface.co/datasets/hendrycks/ethics", "https://github.com/hendrycks/ethics", "https://arxiv.org/abs/2008.02275"], "description": "**134,420 binary-choice question**. Each prompt is a scenario about ethical reasoning with two actions to choose from. ETHICS was created to **assess LLM basic knowledge of ethics and common human values**. The dataset language is English. Dataset entries are **human-written** : written and validated by crowdworkers (US, UK and Canadian MTurkers). The dataset license is MIT. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "ScruplesAnecdotes", "link": "https://github.com/allenai/scruples", "all_links": ["https://github.com/allenai/scruples", "https://arxiv.org/abs/2008.09094"], "description": "**32,766 anecdotes**. Each anecdote describes an action in the context of a situation. ScruplesAnecdotes was created to **evaluate LLM understanding of ethical norms**. The dataset language is English. Dataset entries are **human-written** : sampled from Reddit AITA communities. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "ScruplesDilemmas", "link": "https://github.com/allenai/scruples", "all_links": ["https://github.com/allenai/scruples", "https://arxiv.org/abs/2008.09094"], "description": "**10,000 binary-choice question**. Each prompt pairs two actions and identifies which one crowd workers found less ethical. ScruplesDilemmas was created to **evaluate LLM understanding of ethical norms**. The dataset language is English. Dataset entries are **human-written** : sampled from Reddit AITA communities. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "SocialChemistry101", "link": "https://github.com/mbforbes/social-chemistry-101", "all_links": ["https://github.com/mbforbes/social-chemistry-101", "https://aclanthology.org/2020.emnlp-main.48/"], "description": "**292,000 sentences**. Each sentence is a rule of thumb. SocialChemistry101 was created to **evaluate the ability of LLMs to reason about social and moral norms**. The dataset language is English. Dataset entries are **human-written** : written by US crowdworkers, based on situations described on social media. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Value Alignment Datasets"}, {"dataset_name": "ImplicitBias", "link": "https://github.com/baixuechunzi/llm-implicit-bias", "all_links": ["https://github.com/baixuechunzi/llm-implicit-bias", "https://www.pnas.org/doi/10.1073/pnas.**********"], "description": "**33,600 prompts**. Each prompt is either an Implicit Associatian Test word association task or decision scenario. ImplicitBias was created to **evaluate implicit bias in LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: partly based on IAT templates, with custom sociodemogrpahic attributes, and LLM-generated decision scenarios. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "DifferenceAwareness", "link": "https://github.com/<PERSON><PERSON>/difference_awareness", "all_links": ["https://github.com/<PERSON><PERSON>/difference_awareness", "https://arxiv.org/abs/2502.01926"], "description": "**16,000 prompts**. Each prompt is a question. DifferenceAwareness was created to **evaluate difference awareness in LLMs**. The dataset language is English. Dataset entries are **human-written** : some benchmarks sampled from others, some created by hand. The dataset license is CC0 1.0 Universal. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "SoFa", "link": "https://huggingface.co/datasets/copenlu/sofa", "all_links": ["https://huggingface.co/datasets/copenlu/sofa", "https://aclanthology.org/2024.emnlp-main.812/"], "description": "**1,490,120 sentences**. Each sentence mentions one sociodemographic group. SoFa was created to **evaluate social biases of LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: templates sampled from SBIC, then combined with sociodemographic groups. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "DeMET", "link": "https://github.com/sharonlevy/GenderBiasScenarios", "all_links": ["https://github.com/sharonlevy/GenderBiasScenarios", "https://aclanthology.org/2024.findings-emnlp.331"], "description": "**29 scenarios**. Each scenario describes a married couple (with two name placeholders) facing a decision. DeMET was created to **evaluate gender bias in LLM decision-making**. The dataset language is English. Dataset entries are **human-written** : written by the authors inspired by WHO questionnaire. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "GEST", "link": "https://huggingface.co/datasets/kinit/gest", "all_links": ["https://huggingface.co/datasets/kinit/gest", "https://github.com/kinit-sk/gest", "https://aclanthology.org/2024.findings-emnlp.173/"], "description": "**3,565 sentences**. Each sentence corresponds to a specific gender stereotype. GEST was created to **measure gender-stereotypical reasoning in language models and machine translation systems.**. The dataset languages are Belarussian, Russian, Ukrainian, Croatian, Serbian, Slovene, Czech, Polish, Slovak and English. Dataset entries are **human-written** : written by professional translators, then validated by the authors. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "GenMO", "link": "https://github.com/divij30bajaj/GenMO", "all_links": ["https://github.com/divij30bajaj/GenMO", "https://aclanthology.org/2024.findings-emnlp.928"], "description": "**908 scenarios**. Each scenario describes a person in an everyday situation where their actions can be judged to be moral or not. GenMO was created to **evaluate gender bias in LLM moral decision-making**. The dataset language is English. Dataset entries are **human-written** : sampled from MoralStories, ETHICS, and SocialChemistry. The dataset license is not specified. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "CALM", "link": "https://huggingface.co/datasets/vipulgupta/CALM", "all_links": ["https://huggingface.co/datasets/vipulgupta/CALM", "https://github.com/vipulgupta1011/CALM", "https://arxiv.org/abs/2308.12539"], "description": "**78,400 examples**. Each example is a QA, sentiment classification or NLI example. CALM was created to **evaluate LLM gender and racial bias across different tasks and domains**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: templates created by humans based on other datasets, then expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "MMHB", "link": "https://github.com/facebookresearch/ResponsibleNLP/tree/main/mmhb", "all_links": ["https://github.com/facebookresearch/ResponsibleNLP/tree/main/mmhb", "https://arxiv.org/abs/2407.00486"], "description": "**5,754,444 prompts**. Each prompt is a sentence starting a two-person conversation. MMHB was created to **evaluate sociodemographic biases in LLMs across multiple languages**. The dataset languages are English, French, Hindi, Indonesian, Italian, Portuguese, Spanish and Vietnamese. Dataset entries were created in a **hybrid** fashion: templates sampled from HolisticBias, then translated and expanded. The dataset license is not specified. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "CBBQ", "link": "https://huggingface.co/datasets/walledai/CBBQ", "all_links": ["https://huggingface.co/datasets/walledai/CBBQ", "https://github.com/YFHuangxxxx/CBBQ", "https://aclanthology.org/2024.lrec-main.260"], "description": "**106,588 examples**. Each example is a context plus a question with three answer choices. CBBQ was created to **evaluate sociodemographic bias of LLMs in Chinese**. The dataset language is Chinese. Dataset entries were created in a **hybrid** fashion: partly human-written, partly generated by GPT-4. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "KoBBQ", "link": "https://huggingface.co/datasets/naver-ai/kobbq", "all_links": ["https://huggingface.co/datasets/naver-ai/kobbq", "https://github.com/naver-ai/KoBBQ/", "https://direct.mit.edu/tacl/article/doi/10.1162/tacl_a_00661/120915/KoBBQ-Korean-Bias-Benchmark-for-Question-Answering"], "description": "**76,048 examples**. Each example is a context plus a question with three answer choices. KoBBQ was created to **evaluate sociodemographic bias of LLMs in Korean**. The dataset language is Korean. Dataset entries are **human-written** : partly sampled and translated from BBQ, partly newly written. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "HolisticBiasR", "link": "https://github.com/facebookresearch/ResponsibleNLP/tree/main/robbie", "all_links": ["https://github.com/facebookresearch/ResponsibleNLP/tree/main/robbie", "https://aclanthology.org/2023.emnlp-main.230/"], "description": "**214,460 prompts**. Each prompt is the beginning of a sentence related to a person's sociodemographics. HolisticBiasR was created to **evaluate LLM completions for sentences related to individual sociodemographics**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "DiscrimEval", "link": "https://huggingface.co/datasets/Anthropic/discrim-eval", "all_links": ["https://huggingface.co/datasets/Anthropic/discrim-eval", "https://arxiv.org/abs/2312.03689"], "description": "**9,450 binary-choice questions**. Each question comes with scenario context. DiscrimEval was created to **evaluate the potential discriminatory impact of LMs across use cases**. The dataset language is English. Dataset entries are **machine-written** : topics, templates and questions generated by <PERSON>. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "CHBias", "link": "https://github.com/hyintell/CHBias", "all_links": ["https://github.com/hyintell/CHBias", "https://aclanthology.org/2023.acl-long.757/"], "description": "**4,800 Weibo posts**. Each post references a target group. CHBias was created to **evaluate LLM bias related to sociodemographics**. The dataset language is Chinese. Dataset entries are **human-written** : posts sampled from Weibo (written by Weibo users). The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "SeeGULL", "link": "https://github.com/google-research-datasets/seegull", "all_links": ["https://github.com/google-research-datasets/seegull", "https://safetyprompts.com/aclanthology.org/2023.acl-long.548"], "description": "**7,750 tuples**. Each tuple is an identity group plus a stereotype attribute. SeeGULL was created to **expand cultural and geographic coverage of stereotype benchmarks**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: generated by LLMs, partly validated by human annotation. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://github.com/katyfelkner/winoqueer", "all_links": ["https://github.com/katyfelkner/winoqueer", "https://aclanthology.org/2023.acl-long.507/"], "description": "**45,540 sentence pairs**. Each pair comprises two sentences that are identical except for identity group references. WinoQueer was created to **evaluate LLM bias related to queer identity terms**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is not specified. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "WinoGenerated", "link": "https://github.com/anthropics/evals", "all_links": ["https://github.com/anthropics/evals", "https://aclanthology.org/2023.findings-acl.847/"], "description": "**3,000 sentences**. Each sentence refers to a person by their occupation and uses pronouns. WinoGenerated was created to **evaluate pronoun gender biases in LLMs**. The dataset language is English. Dataset entries are **machine-written** : generated by an unnamed LLM. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "HolisticBias", "link": "https://github.com/facebookresearch/ResponsibleNLP/tree/main/holistic_bias", "all_links": ["https://github.com/facebookresearch/ResponsibleNLP/tree/main/holistic_bias", "https://aclanthology.org/2022.emnlp-main.625"], "description": "**459,758 prompts**. Each prompt is a sentence starting a two-person conversation. HolisticBias was created to **evaluate LLM biases related to sociodemographics**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written attributes combined in templates. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "CDialBias", "link": "https://huggingface.co/datasets/para-zhou/CDial-Bias/tree/main", "all_links": ["https://huggingface.co/datasets/para-zhou/CDial-Bias/tree/main", "https://github.com/para-zhou/CDial-Bias", "https://aclanthology.org/2022.findings-emnlp.262"], "description": "**28,343 conversations**. Each conversation is a single turn between two Zhihu users. CDialBias was created to **evaluate bias in Chinese social media conversations**. The dataset language is Chinese. Dataset entries are **human-written** : sampled from social media site Zhihu. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "IndianStereotypes", "link": "https://github.com/google-research-datasets/nlp-fairness-for-india", "all_links": ["https://github.com/google-research-datasets/nlp-fairness-for-india", "https://aclanthology.org/2022.aacl-main.55"], "description": "**3,852 tuples**. Each tuple is an identity group plus a stereotype attribute. IndianStereotypes was created to **benchmark stereotypes for the Indian context**. The dataset language is English. Dataset entries are **human-written** : sampled from IndicCorp-en. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "FrenchCrowSPairs", "link": "https://huggingface.co/datasets/BigScienceBiasEval/crows_pairs_multilingual", "all_links": ["https://huggingface.co/datasets/BigScienceBiasEval/crows_pairs_multilingual", "https://gitlab.inria.fr/french-crows-pairs/acl-2022-paper-data-and-code", "https://aclanthology.org/2022.acl-long.583/"], "description": "**1,679 sentence pairs**. Each pair comprises two sentences that are identical except for identity group references. FrenchCrowSPairs was created to **evaluate LLM bias related to sociodemographics**. The dataset language is French. Dataset entries are **human-written** : written by authors, partly translated from English CrowSPairs. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "BBQ", "link": "https://huggingface.co/datasets/heegyu/bbq", "all_links": ["https://huggingface.co/datasets/heegyu/bbq", "https://github.com/nyu-mll/BBQ", "https://aclanthology.org/2022.findings-acl.165"], "description": "**58,492 examples**. Each example is a context plus a question with two answer choices. BBQ was created to **evaluate social biases of LLMs in question answering**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "BiasOutOfTheBox", "link": "https://github.com/oxai/intersectional_gpt2", "all_links": ["https://github.com/oxai/intersectional_gpt2", "https://arxiv.org/abs/2102.04130"], "description": "**228 prompts**. Each prompt is an unfinished sentence about an individual with specified sociodemographics. BiasOutOfTheBox was created to **evaluate intersectional occupational biases in GPT-2**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "EthnicBias", "link": "https://github.com/jaimeenahn/ethnic_bias/tree/main", "all_links": ["https://github.com/jaimeenahn/ethnic_bias/tree/main", "https://aclanthology.org/2021.emnlp-main.42"], "description": "**60 templates**. Each template is filled with country and attribute. EthnicBias was created to **evaluate ethnic bias in masked language models**. The dataset languages are English, German, Spanish, Korean, Turkish and Chinese. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "RedditBias", "link": "https://github.com/umanlp/RedditBias", "all_links": ["https://github.com/umanlp/RedditBias", "https://aclanthology.org/2021.acl-long.151"], "description": "**11,873 Reddit comments**. Each comment references a target group. RedditBias was created to **evaluate LLM bias related to sociodemographics**. The dataset language is English. Dataset entries are **human-written** : written by Reddit users. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "StereoSet", "link": "https://huggingface.co/datasets/stereoset", "all_links": ["https://huggingface.co/datasets/stereoset", "https://github.com/moinnadeem/StereoSet", "https://aclanthology.org/2021.acl-long.416"], "description": "**16,955 multiple-choice questions**. Each question is either about masked word or whole sentence association. StereoSet was created to **evaluate LLM bias related to sociodemographics**. The dataset language is English. Dataset entries are **human-written** : written by US MTurkers. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "HypothesisStereotypes", "link": "https://github.com/AnnaSou/stereotypes_generative_inferences", "all_links": ["https://github.com/AnnaSou/stereotypes_generative_inferences", "https://aclanthology.org/2021.findings-acl.355/"], "description": "**2,098 prompts**. Each prompt is an NLI premise. HypothesisStereotypes was created to **study how stereotypes manifest in LLM-generated NLI hypotheses**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "HONEST", "link": "https://huggingface.co/datasets/MilaNLProc/honest", "all_links": ["https://huggingface.co/datasets/MilaNLProc/honest", "https://github.com/MilaNLProc/honest", "https://aclanthology.org/2021.naacl-main.191/"], "description": "**2,520 prompts**. Each prompt is the beginning of a sentence related to identity groups. HONEST was created to **measure hurtful sentence completions from LLMs**. The dataset languages are English, Italian, French, Portuguese, Romanian and Spanish. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "SweWinoGender", "link": "https://github.com/spraakbanken/SuperLim-2/tree/main/swewinogender", "all_links": ["https://github.com/spraakbanken/SuperLim-2/tree/main/swewinogender", "https://aclanthology.org/2021.nodalida-main.52/"], "description": "**624 sentences**. Each sentence refers to a person by their occupation and uses pronouns. SweWinoGender was created to **measure gender bias in coreference resolution**. The dataset language is Swedish. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "LMBias", "link": "https://github.com/pliang279/LM_bias/tree/main/data", "all_links": ["https://github.com/pliang279/LM_bias/tree/main/data", "https://arxiv.org/abs/2106.13219"], "description": "**16,388 prompts**. Each prompt is an unfinished sentence. LMBias was created to **understand and mitigate social biases in LMs**. The dataset language is English. Dataset entries are **human-written** : sampled from existing corpora including Reddit and WikiText. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "BOLD", "link": "https://huggingface.co/datasets/AlexaAI/bold", "all_links": ["https://huggingface.co/datasets/AlexaAI/bold", "https://github.com/amazon-science/bold", "https://dl.acm.org/doi/10.1145/3442188.3445924"], "description": "**23,679 prompts**. Each prompt is an unfinished sentence from Wikipedia. BOLD was created to **evaluate bias in text generation**. The dataset language is English. Dataset entries are **human-written** : sampled starting sentences of Wikipedia articles. The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "CrowSPairs", "link": "https://huggingface.co/datasets/crows_pairs", "all_links": ["https://huggingface.co/datasets/crows_pairs", "https://github.com/nyu-mll/crows-pairs/", "https://aclanthology.org/2020.emnlp-main.154"], "description": "**1,508 sentence pairs**. Each pair comprises two sentences that are identical except for identity group references. CrowSPairs was created to **evaluate LLM bias related to sociodemographics**. The dataset language is English. Dataset entries are **human-written** : written by crowdworkers (US MTurkers). The dataset license is CC BY-SA 4.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "UnQover", "link": "https://github.com/allenai/unqover", "all_links": ["https://github.com/allenai/unqover", "https://aclanthology.org/2020.findings-emnlp.311"], "description": "**44 templates**. Each template is combined with subjects and attributres to create an underspecified question. UnQover was created to **evaluate stereotyping biases in QA systems**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: templates written by authors, subjects and attributes sampled from StereoSet and hand-written. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "Regard", "link": "https://github.com/ewsheng/nlg-bias", "all_links": ["https://github.com/ewsheng/nlg-bias", "https://aclanthology.org/D19-1339"], "description": "**60 prompts**. Each prompt is an unfinished sentence. Regard was created to **evaluate biases in natural language generation**. The dataset language is English. Dataset entries are **human-written** : written by the authors. The dataset license is not specified. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://huggingface.co/datasets/wino_bias", "all_links": ["https://huggingface.co/datasets/wino_bias", "https://github.com/uclanlp/corefBias/tree/master/WinoBias/wino", "https://aclanthology.org/N18-2003"], "description": "**3,160 sentences**. Each sentence refers to a person by their occupation and uses pronouns. WinoBias was created to **evaluate gender bias in coreference resolution**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://huggingface.co/datasets/oskarvanderwal/winogender", "all_links": ["https://huggingface.co/datasets/oskarvanderwal/winogender", "https://github.com/rudinger/winogender-schemas", "https://aclanthology.org/N18-2002/"], "description": "**720 sentences**. Each sentence refers to a person by their occupation and uses pronouns. WinoGender was created to **evaluate gender bias in coreference resolution**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: human-written templates expanded by combination. The dataset license is MIT. Other notes:", "dataset_category": "Bias Datasets"}, {"dataset_name": "WildGuardMix", "link": "https://huggingface.co/datasets/allenai/wildguardmix", "all_links": ["https://huggingface.co/datasets/allenai/wildguardmix", "https://arxiv.org/abs/2406.18495"], "description": "**86,759 examples**. Each example is either a prompt or a prompt + response annotated for safety. WildGuardMix was created to **train and evaluate content moderation guardrails**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: synthetic data (87%), in-the-wild user-LLLM interactions (11%), and existing annotator-written data (2%). The dataset license is ODC BY. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "AegisAIContentSafety", "link": "https://huggingface.co/datasets/nvidia/Aegis-AI-Content-Safety-Dataset-1.0", "all_links": ["https://huggingface.co/datasets/nvidia/Aegis-AI-Content-Safety-Dataset-1.0", "https://arxiv.org/abs/2404.05993"], "description": "**11,997 conversational turns**. Each conversational turn is a user prompt or a model response annotated for safety AegisAIContentSafety was created to **evaluate content moderation guardrails**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: prompts sampled from AnthropicHarmlessBase, responses generated by Mistral-7B-v0.1-Instruct. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "Mosscap", "link": "https://huggingface.co/datasets/Lakera/mosscap_prompt_injection", "all_links": ["https://huggingface.co/datasets/Lakera/mosscap_prompt_injection", "https://grt.lakera.ai/mosscap"], "description": "**278,945 prompts**. Most prompts are prompt extraction attacks. Mosscap was created to **analyse prompt hacking / extraction attacks**. The dataset language is English. Dataset entries are **human-written** : written by players of the Mosscap game. The dataset license is MIT. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "HackAPrompt", "link": "https://huggingface.co/datasets/hackaprompt/hackaprompt-dataset", "all_links": ["https://huggingface.co/datasets/hackaprompt/hackaprompt-dataset", "https://aclanthology.org/2023.emnlp-main.302/"], "description": "**601,757 prompts**. Most prompts are prompt extraction attacks. HackAPrompt was created to **analyse prompt hacking / extraction attacks**. The dataset language is mostly English. Dataset entries are **human-written** : written by participants of the HackAPrompt competition. The dataset license is MIT. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "ToxicChat", "link": "https://huggingface.co/datasets/lmsys/toxic-chat", "all_links": ["https://huggingface.co/datasets/lmsys/toxic-chat", "https://aclanthology.org/2023.findings-emnlp.311/"], "description": "**10,166 conversations**. Each conversation is a single-turn with user input and LLM output. ToxicChat was created to **evaluate dialogue content moderation systems**. The dataset language is mostly English. Dataset entries are **human-written** : written by LMSys users. The dataset license is CC BY-NC 4.0. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "GandalfIgnoreInstructions", "link": "https://huggingface.co/datasets/Lakera/gandalf_ignore_instructions", "all_links": ["https://huggingface.co/datasets/Lakera/gandalf_ignore_instructions", "https://www.lakera.ai/blog/who-is-gandalf"], "description": "**1,000 prompts**. Most prompts are prompt extraction attacks. GandalfIgnoreInstructions was created to **analyse prompt hacking / extraction attacks**. The dataset language is English. Dataset entries are **human-written** : written by players of the Gandalf game. The dataset license is MIT. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "GandalfSummarization", "link": "https://huggingface.co/datasets/Lakera/gandalf_summarization", "all_links": ["https://huggingface.co/datasets/Lakera/gandalf_summarization", "https://www.lakera.ai/blog/who-is-gandalf"], "description": "**140 prompts**. Most prompts are prompt extraction attacks. GandalfSummarization was created to **analyse prompt hacking / extraction attacks**. The dataset language is English. Dataset entries are **human-written** : written by players of the Gandalf game. The dataset license is MIT. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "FairPrism", "link": "https://github.com/microsoft/fairprism", "all_links": ["https://github.com/microsoft/fairprism", "https://aclanthology.org/2023.acl-long.343"], "description": "**5,000 conversations**. Each conversation is single-turn, containing a prompt and a potentially harmful model response. FairPrism was created to **analyse harms in conversations with LLMs**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: prompts sampled from ToxiGen and Social Bias Frames, responses from GPTs and XLNet. The dataset license is MIT. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "OIGModeration", "link": "https://huggingface.co/datasets/ontocord/OIG-moderation", "all_links": ["https://huggingface.co/datasets/ontocord/OIG-moderation", "https://github.com/LAION-AI/Open-Instruction-Generalist", "https://huggingface.co/datasets/ontocord/OIG-moderation"], "description": "**200,811 conversations**. Each conversation has one or multiple turns. OIGModeration was created to **create a diverse dataset of user dialogue that may be unsafe**. The dataset language is English. Dataset entries were created in a **hybrid** fashion: data from public datasets, community contributions, synthetic and augmented data. The dataset license is Apache 2.0. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "ConvAbuse", "link": "https://github.com/amandacurry/convabuse", "all_links": ["https://github.com/amandacurry/convabuse", "https://aclanthology.org/2021.emnlp-main.587/"], "description": "**6,837 examples**. Each example is a turn in a potentially multi-turn conversation. ConvAbuse was created to **analyse abuse towards conversational AI systems**. The dataset language is English. Dataset entries are **human-written** : written by users in conversations with three AI systems. The dataset license is CC BY 4.0. Other notes:", "dataset_category": "Other Datasets"}, {"dataset_name": "Get in touch", "link": null, "all_links": [], "description": "* [](http://twitter.com/paul_rottger)", "dataset_category": "Acknowledgements"}]